// DOM Elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');
const navLinks = document.querySelectorAll('.nav-link');
const heroButtons = document.querySelectorAll('.btn');
const featureCards = document.querySelectorAll('.feature-card');
const newsCards = document.querySelectorAll('.news-card');
const infoCards = document.querySelectorAll('.info-card');

// Mobile Menu Toggle
hamburger.addEventListener('click', () => {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on a link
navLinks.forEach(link => {
    link.addEventListener('click', () => {
        hamburger.classList.remove('active');
        navMenu.classList.remove('active');
    });
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Animate elements on scroll
const animateOnScroll = (elements) => {
    elements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(element);
    });
};

// Apply animations to different sections
if (featureCards.length > 0) {
    animateOnScroll(featureCards);
}

if (newsCards.length > 0) {
    animateOnScroll(newsCards);
}

if (infoCards.length > 0) {
    animateOnScroll(infoCards);
}

// Button hover effects
heroButtons.forEach(button => {
    button.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-3px) scale(1.05)';
    });
    
    button.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});

// Parallax effect for background
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const parallax = document.querySelector('.background-animation');
    const speed = scrolled * 0.5;
    
    if (parallax) {
        parallax.style.transform = `translateY(${speed}px)`;
    }
});

// Dynamic particle generation
function createParticle() {
    const particle = document.createElement('div');
    particle.className = 'dynamic-particle';
    particle.style.cssText = `
        position: fixed;
        width: 2px;
        height: 2px;
        background: #00ffff;
        border-radius: 50%;
        pointer-events: none;
        z-index: -1;
        box-shadow: 0 0 10px #00ffff;
    `;
    
    // Random starting position
    particle.style.left = Math.random() * window.innerWidth + 'px';
    particle.style.top = window.innerHeight + 'px';
    
    document.body.appendChild(particle);
    
    // Animate particle
    const animation = particle.animate([
        {
            transform: 'translateY(0px) translateX(0px)',
            opacity: 0
        },
        {
            transform: `translateY(-${window.innerHeight + 100}px) translateX(${(Math.random() - 0.5) * 200}px)`,
            opacity: 1
        },
        {
            transform: `translateY(-${window.innerHeight + 200}px) translateX(${(Math.random() - 0.5) * 400}px)`,
            opacity: 0
        }
    ], {
        duration: 8000 + Math.random() * 4000,
        easing: 'linear'
    });
    
    animation.onfinish = () => {
        particle.remove();
    };
}

// Create particles periodically
setInterval(createParticle, 3000);

// Server status simulation
function updateServerStatus() {
    const statusElement = document.querySelector('.info-status');
    const playerCountElement = document.querySelector('.info-number');
    
    if (statusElement && playerCountElement) {
        // Simulate player count changes
        const currentCount = parseInt(playerCountElement.textContent);
        const change = Math.floor(Math.random() * 10) - 5; // -5 to +5
        const newCount = Math.max(0, currentCount + change);
        
        // Animate number change
        const countAnimation = playerCountElement.animate([
            { transform: 'scale(1)' },
            { transform: 'scale(1.1)' },
            { transform: 'scale(1)' }
        ], {
            duration: 500,
            easing: 'ease-in-out'
        });
        
        countAnimation.onfinish = () => {
            playerCountElement.textContent = newCount;
        };
    }
}

// Update server status every 30 seconds
setInterval(updateServerStatus, 30000);

// Typing effect for hero title
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';
    
    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

// Initialize typing effect when page loads
window.addEventListener('load', () => {
    const heroTitle = document.querySelector('.hero-title');
    if (heroTitle) {
        const originalText = heroTitle.textContent;
        typeWriter(heroTitle, originalText, 150);
    }
});

// Card tilt effect
function addTiltEffect(cards) {
    cards.forEach(card => {
        card.addEventListener('mousemove', (e) => {
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;
            
            card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
        });
    });
}

// Apply tilt effect to cards
if (featureCards.length > 0) {
    addTiltEffect(featureCards);
}

if (newsCards.length > 0) {
    addTiltEffect(newsCards);
}

if (infoCards.length > 0) {
    addTiltEffect(infoCards);
}

// Navbar background opacity on scroll
window.addEventListener('scroll', () => {
    const header = document.querySelector('.header');
    const scrolled = window.pageYOffset;
    
    if (scrolled > 50) {
        header.style.background = 'rgba(10, 10, 10, 0.98)';
    } else {
        header.style.background = 'rgba(10, 10, 10, 0.95)';
    }
});

// Loading animation
window.addEventListener('load', () => {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// Console welcome message
console.log(`
%c🎮 ETERNAL GAMING SERVER 🎮
%cHoş geldiniz! Web sitesi başarıyla yüklendi.
%cGeliştirici: Eternal Gaming Team
`, 
'color: #00bfff; font-size: 20px; font-weight: bold;',
'color: #87ceeb; font-size: 14px;',
'color: #b0b0b0; font-size: 12px;'
);

// Error handling
window.addEventListener('error', (e) => {
    console.error('Bir hata oluştu:', e.error);
});

// Performance monitoring
if ('performance' in window) {
    window.addEventListener('load', () => {
        setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log(`Sayfa yükleme süresi: ${Math.round(perfData.loadEventEnd - perfData.loadEventStart)}ms`);
        }, 0);
    });
}
