/* Reset ve <PERSON><PERSON> */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-blue: #00bfff;
    --secondary-blue: #1e90ff;
    --dark-blue: #0066cc;
    --light-blue: #87ceeb;
    --accent-cyan: #00ffff;
    --dark-bg: #0a0a0a;
    --card-bg: rgba(30, 144, 255, 0.1);
    --text-light: #ffffff;
    --text-gray: #b0b0b0;
    --gradient-primary: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    --gradient-secondary: linear-gradient(135deg, var(--dark-blue), var(--primary-blue));
    --shadow-blue: 0 0 20px rgba(0, 191, 255, 0.3);
    --shadow-glow: 0 0 30px rgba(0, 255, 255, 0.2);
}

body {
    font-family: 'Exo 2', sans-serif;
    background: var(--dark-bg);
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Arka Plan Animasyonu */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: radial-gradient(circle at 20% 50%, rgba(0, 191, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(30, 144, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(0, 255, 255, 0.05) 0%, transparent 50%);
}

.floating-particles::before,
.floating-particles::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--accent-cyan);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
    box-shadow: var(--shadow-glow);
}

.floating-particles::before {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-particles::after {
    top: 60%;
    right: 15%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Header ve Navigasyon */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 191, 255, 0.2);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.logo {
    display: flex;
    align-items: center;
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 900;
    color: var(--primary-blue);
    text-shadow: var(--shadow-blue);
}

.logo i {
    margin-right: 10px;
    font-size: 2rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: var(--text-gray);
    font-weight: 600;
    padding: 12px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left 0.3s ease;
    z-index: -1;
}

.nav-link:hover::before,
.nav-link.active::before {
    left: 0;
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow-blue);
}

.nav-link i {
    font-size: 1.1rem;
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--primary-blue);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Ana İçerik */
.main-content {
    margin-top: 70px;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0 20px;
    position: relative;
}

.hero-content {
    max-width: 800px;
    margin-bottom: 50px;
}

.hero-title {
    font-family: 'Orbitron', monospace;
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 20px;
    line-height: 1.1;
}

.title-line {
    display: block;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: var(--shadow-blue);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { filter: drop-shadow(0 0 10px rgba(0, 191, 255, 0.5)); }
    to { filter: drop-shadow(0 0 20px rgba(0, 191, 255, 0.8)); }
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--text-gray);
    margin-bottom: 40px;
    font-weight: 300;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border: none;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-light);
    box-shadow: var(--shadow-blue);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 191, 255, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
}

.btn-secondary:hover {
    background: var(--primary-blue);
    color: var(--text-light);
    transform: translateY(-3px);
}

/* Server Info Cards */
.server-info {
    display: flex;
    gap: 30px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 50px;
}

.info-card {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 191, 255, 0.2);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 15px;
    min-width: 200px;
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-blue);
    border-color: var(--primary-blue);
}

.info-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--text-light);
}

.info-content h3 {
    font-size: 0.9rem;
    color: var(--text-gray);
    margin-bottom: 5px;
}

.info-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-blue);
}

.info-status.online {
    color: #00ff88;
    font-weight: 600;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Section Titles */
.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 50px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Features Section */
.features-section {
    padding: 100px 0;
    background: rgba(0, 0, 0, 0.3);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature-card {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 191, 255, 0.2);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, var(--primary-blue), transparent);
    animation: rotate 4s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-card:hover::before {
    opacity: 0.1;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-blue);
    border-color: var(--primary-blue);
}

@keyframes rotate {
    100% { transform: rotate(360deg); }
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: var(--text-light);
    position: relative;
    z-index: 1;
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: var(--primary-blue);
    position: relative;
    z-index: 1;
}

.feature-card p {
    color: var(--text-gray);
    line-height: 1.6;
    position: relative;
    z-index: 1;
}

/* News Section */
.news-section {
    padding: 100px 0;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.news-card {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 191, 255, 0.2);
    border-radius: 15px;
    padding: 25px;
    transition: all 0.3s ease;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-blue);
    border-color: var(--primary-blue);
}

.news-date {
    color: var(--primary-blue);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.news-card h3 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: var(--text-light);
}

.news-card p {
    color: var(--text-gray);
    margin-bottom: 15px;
    line-height: 1.6;
}

.read-more {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.read-more:hover {
    color: var(--accent-cyan);
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.8);
    border-top: 1px solid rgba(0, 191, 255, 0.2);
    padding: 50px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h3 {
    color: var(--primary-blue);
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.footer-section p,
.footer-section ul {
    color: var(--text-gray);
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: var(--text-gray);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-blue);
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 15px;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-blue);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 191, 255, 0.1);
    color: var(--text-gray);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(10, 10, 10, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        backdrop-filter: blur(10px);
        border-top: 1px solid rgba(0, 191, 255, 0.2);
        padding: 20px 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .server-info {
        flex-direction: column;
        align-items: center;
    }

    .section-title {
        font-size: 2rem;
    }
}
