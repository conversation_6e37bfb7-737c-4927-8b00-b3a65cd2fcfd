<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eternal Gaming Server - Market</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .market-hero {
            background: linear-gradient(135deg, rgba(0, 191, 255, 0.1), rgba(30, 144, 255, 0.1));
            padding: 120px 0 80px;
            text-align: center;
        }
        
        .market-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .category-tabs {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }
        
        .tab-btn {
            padding: 12px 25px;
            background: var(--card-bg);
            border: 1px solid rgba(0, 191, 255, 0.2);
            border-radius: 25px;
            color: var(--text-gray);
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tab-btn.active,
        .tab-btn:hover {
            background: var(--gradient-primary);
            color: var(--text-light);
            border-color: var(--primary-blue);
            transform: translateY(-2px);
            box-shadow: var(--shadow-blue);
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .product-card {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 191, 255, 0.2);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .product-card:hover {
            transform: translateY(-10px);
            border-color: var(--primary-blue);
            box-shadow: var(--shadow-blue);
        }
        
        .product-image {
            height: 200px;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: var(--text-light);
            position: relative;
            overflow: hidden;
        }
        
        .product-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }
        
        .product-card:hover .product-image::before {
            left: 100%;
        }
        
        .product-info {
            padding: 25px;
        }
        
        .product-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.3rem;
            color: var(--primary-blue);
            margin-bottom: 10px;
        }
        
        .product-description {
            color: var(--text-gray);
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .product-features {
            list-style: none;
            margin-bottom: 20px;
        }
        
        .product-features li {
            padding: 5px 0;
            color: var(--text-gray);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .product-features li i {
            color: var(--primary-blue);
            width: 16px;
        }
        
        .product-price {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .price {
            font-family: 'Orbitron', monospace;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-cyan);
        }
        
        .old-price {
            text-decoration: line-through;
            color: var(--text-gray);
            font-size: 1rem;
        }
        
        .discount-badge {
            background: #ff4757;
            color: var(--text-light);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            position: absolute;
            top: 15px;
            right: 15px;
        }
        
        .buy-btn {
            width: 100%;
            padding: 15px;
            background: var(--gradient-primary);
            border: none;
            border-radius: 10px;
            color: var(--text-light);
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .buy-btn:hover {
            background: var(--gradient-secondary);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 191, 255, 0.3);
        }
        
        .payment-methods {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 191, 255, 0.2);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-top: 50px;
        }
        
        .payment-methods h3 {
            font-family: 'Orbitron', monospace;
            color: var(--primary-blue);
            margin-bottom: 20px;
        }
        
        .payment-icons {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .payment-icon {
            width: 60px;
            height: 60px;
            background: var(--gradient-primary);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--text-light);
            transition: all 0.3s ease;
        }
        
        .payment-icon:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-blue);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .special-offer {
            background: linear-gradient(135deg, #ff6b6b, #ff8e53);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }
        
        .special-offer::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: rotate 4s linear infinite;
        }
        
        .special-offer h3 {
            font-family: 'Orbitron', monospace;
            font-size: 1.5rem;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .special-offer p {
            font-size: 1.1rem;
            margin-bottom: 0;
            position: relative;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="background-animation">
        <div class="floating-particles"></div>
    </div>

    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <i class="fas fa-gamepad"></i>
                    <span>ETERNAL</span>
                </div>
                
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="fas fa-home"></i>
                            <span>Ana Sayfa</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="rules.html" class="nav-link">
                            <i class="fas fa-scroll"></i>
                            <span>Kurallar</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="market.html" class="nav-link active">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Market</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="contact.html" class="nav-link">
                            <i class="fas fa-envelope"></i>
                            <span>İletişim</span>
                        </a>
                    </li>
                </ul>

                <div class="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <main class="main-content">
        <section class="market-hero">
            <div class="market-container">
                <h1 class="section-title">
                    <i class="fas fa-shopping-cart"></i>
                    Eternal Market
                </h1>
                <p style="font-size: 1.2rem; color: var(--text-gray); margin-bottom: 0;">
                    Oyun deneyiminizi geliştirin! Premium paketler ve özel eşyalar burada
                </p>
            </div>
        </section>

        <section style="padding: 50px 0;">
            <div class="market-container">
                <div class="special-offer">
                    <h3>🎉 Özel Kampanya! 🎉</h3>
                    <p>Bu hafta tüm VIP paketlerde %25 indirim! Fırsatı kaçırmayın!</p>
                </div>

                <div class="category-tabs">
                    <button class="tab-btn active" onclick="showTab('vip')">
                        <i class="fas fa-crown"></i>
                        VIP Paketler
                    </button>
                    <button class="tab-btn" onclick="showTab('items')">
                        <i class="fas fa-gem"></i>
                        Özel Eşyalar
                    </button>
                    <button class="tab-btn" onclick="showTab('cosmetics')">
                        <i class="fas fa-palette"></i>
                        Kozmetikler
                    </button>
                    <button class="tab-btn" onclick="showTab('boosters')">
                        <i class="fas fa-rocket"></i>
                        Güçlendiriciler
                    </button>
                </div>

                <div id="vip" class="tab-content active">
                    <div class="products-grid">
                        <div class="product-card">
                            <div class="discount-badge">-25%</div>
                            <div class="product-image">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="product-info">
                                <h3 class="product-title">VIP Bronze</h3>
                                <p class="product-description">Başlangıç seviyesi VIP paketi. Temel ayrıcalıklar ve özel komutlar.</p>
                                <ul class="product-features">
                                    <li><i class="fas fa-check"></i> /fly komutu</li>
                                    <li><i class="fas fa-check"></i> Özel chat rengi</li>
                                    <li><i class="fas fa-check"></i> 2x XP kazancı</li>
                                    <li><i class="fas fa-check"></i> VIP spawn alanı</li>
                                </ul>
                                <div class="product-price">
                                    <div>
                                        <span class="old-price">40₺</span>
                                        <span class="price">30₺</span>
                                    </div>
                                </div>
                                <button class="buy-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    Satın Al
                                </button>
                            </div>
                        </div>

                        <div class="product-card">
                            <div class="discount-badge">-25%</div>
                            <div class="product-image">
                                <i class="fas fa-medal"></i>
                            </div>
                            <div class="product-info">
                                <h3 class="product-title">VIP Silver</h3>
                                <p class="product-description">Orta seviye VIP paketi. Daha fazla ayrıcalık ve özel özellikler.</p>
                                <ul class="product-features">
                                    <li><i class="fas fa-check"></i> Bronze'un tüm özellikleri</li>
                                    <li><i class="fas fa-check"></i> /tp komutu</li>
                                    <li><i class="fas fa-check"></i> 3x XP kazancı</li>
                                    <li><i class="fas fa-check"></i> Özel kit</li>
                                </ul>
                                <div class="product-price">
                                    <div>
                                        <span class="old-price">80₺</span>
                                        <span class="price">60₺</span>
                                    </div>
                                </div>
                                <button class="buy-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    Satın Al
                                </button>
                            </div>
                        </div>

                        <div class="product-card">
                            <div class="discount-badge">-25%</div>
                            <div class="product-image">
                                <i class="fas fa-crown"></i>
                            </div>
                            <div class="product-info">
                                <h3 class="product-title">VIP Gold</h3>
                                <p class="product-description">En üst seviye VIP paketi. Tüm ayrıcalıklar ve özel yetkiler.</p>
                                <ul class="product-features">
                                    <li><i class="fas fa-check"></i> Silver'ın tüm özellikleri</li>
                                    <li><i class="fas fa-check"></i> /god komutu</li>
                                    <li><i class="fas fa-check"></i> 5x XP kazancı</li>
                                    <li><i class="fas fa-check"></i> Özel pet</li>
                                </ul>
                                <div class="product-price">
                                    <div>
                                        <span class="old-price">160₺</span>
                                        <span class="price">120₺</span>
                                    </div>
                                </div>
                                <button class="buy-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    Satın Al
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="items" class="tab-content">
                    <div class="products-grid">
                        <div class="product-card">
                            <div class="product-image">
                                <i class="fas fa-sword"></i>
                            </div>
                            <div class="product-info">
                                <h3 class="product-title">Efsanevi Kılıç</h3>
                                <p class="product-description">Güçlü büyülerle donatılmış efsanevi kılıç.</p>
                                <div class="product-price">
                                    <span class="price">25₺</span>
                                </div>
                                <button class="buy-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    Satın Al
                                </button>
                            </div>
                        </div>

                        <div class="product-card">
                            <div class="product-image">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="product-info">
                                <h3 class="product-title">Koruyucu Zırh</h3>
                                <p class="product-description">Maksimum koruma sağlayan özel zırh seti.</p>
                                <div class="product-price">
                                    <span class="price">35₺</span>
                                </div>
                                <button class="buy-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    Satın Al
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="cosmetics" class="tab-content">
                    <div class="products-grid">
                        <div class="product-card">
                            <div class="product-image">
                                <i class="fas fa-hat-wizard"></i>
                            </div>
                            <div class="product-info">
                                <h3 class="product-title">Büyücü Şapkası</h3>
                                <p class="product-description">Karakterinize özel görünüm kazandıran büyücü şapkası.</p>
                                <div class="product-price">
                                    <span class="price">15₺</span>
                                </div>
                                <button class="buy-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    Satın Al
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="boosters" class="tab-content">
                    <div class="products-grid">
                        <div class="product-card">
                            <div class="product-image">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="product-info">
                                <h3 class="product-title">XP Booster</h3>
                                <p class="product-description">1 hafta boyunca 2x XP kazancı sağlar.</p>
                                <div class="product-price">
                                    <span class="price">10₺</span>
                                </div>
                                <button class="buy-btn">
                                    <i class="fas fa-shopping-cart"></i>
                                    Satın Al
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="payment-methods">
                    <h3>Ödeme Yöntemleri</h3>
                    <div class="payment-icons">
                        <div class="payment-icon">
                            <i class="fab fa-cc-visa"></i>
                        </div>
                        <div class="payment-icon">
                            <i class="fab fa-cc-mastercard"></i>
                        </div>
                        <div class="payment-icon">
                            <i class="fab fa-paypal"></i>
                        </div>
                        <div class="payment-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="payment-icon">
                            <i class="fab fa-bitcoin"></i>
                        </div>
                    </div>
                    <p style="margin-top: 20px; color: var(--text-gray);">
                        Güvenli ödeme altyapısı ile tüm işlemleriniz korunur
                    </p>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Eternal Gaming</h3>
                    <p>En iyi oyun deneyimi için buradayız!</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-discord"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h3>Hızlı Linkler</h3>
                    <ul>
                        <li><a href="index.html">Ana Sayfa</a></li>
                        <li><a href="rules.html">Kurallar</a></li>
                        <li><a href="contact.html">İletişim</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Sunucu Bilgileri</h3>
                    <p>IP: play.eternal.com</p>
                    <p>Port: 25565</p>
                    <p>Versiyon: 1.20.1</p>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Eternal Gaming Server. Tüm hakları saklıdır.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tab buttons
            const tabBtns = document.querySelectorAll('.tab-btn');
            tabBtns.forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked button
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
