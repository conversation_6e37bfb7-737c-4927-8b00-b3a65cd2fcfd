<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eternal Gaming Server - <PERSON><PERSON><PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .contact-hero {
            background: linear-gradient(135deg, rgba(0, 191, 255, 0.1), rgba(30, 144, 255, 0.1));
            padding: 120px 0 80px;
            text-align: center;
        }
        
        .contact-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 50px;
            margin-bottom: 50px;
        }
        
        .contact-info {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 191, 255, 0.2);
            border-radius: 15px;
            padding: 40px;
        }
        
        .contact-form {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 191, 255, 0.2);
            border-radius: 15px;
            padding: 40px;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(0, 191, 255, 0.05);
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .contact-item:hover {
            background: rgba(0, 191, 255, 0.1);
            transform: translateX(10px);
        }
        
        .contact-icon {
            width: 60px;
            height: 60px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--text-light);
            flex-shrink: 0;
        }
        
        .contact-details h4 {
            color: var(--primary-blue);
            margin-bottom: 5px;
            font-family: 'Orbitron', monospace;
        }
        
        .contact-details p {
            color: var(--text-gray);
            margin: 0;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            color: var(--primary-blue);
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 15px;
            background: rgba(0, 191, 255, 0.05);
            border: 1px solid rgba(0, 191, 255, 0.2);
            border-radius: 10px;
            color: var(--text-light);
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 10px rgba(0, 191, 255, 0.3);
            background: rgba(0, 191, 255, 0.1);
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }
        
        .submit-btn {
            width: 100%;
            padding: 15px;
            background: var(--gradient-primary);
            border: none;
            border-radius: 10px;
            color: var(--text-light);
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .submit-btn:hover {
            background: var(--gradient-secondary);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 191, 255, 0.3);
        }
        
        .social-section {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 191, 255, 0.2);
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 50px;
        }
        
        .social-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .social-card {
            background: rgba(0, 191, 255, 0.05);
            border: 1px solid rgba(0, 191, 255, 0.2);
            border-radius: 15px;
            padding: 30px;
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }
        
        .social-card:hover {
            transform: translateY(-10px);
            border-color: var(--primary-blue);
            box-shadow: var(--shadow-blue);
        }
        
        .social-card i {
            font-size: 3rem;
            margin-bottom: 15px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .social-card h4 {
            color: var(--primary-blue);
            margin-bottom: 10px;
            font-family: 'Orbitron', monospace;
        }
        
        .social-card p {
            color: var(--text-gray);
            margin: 0;
        }
        
        .team-section {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 191, 255, 0.2);
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 50px;
        }
        
        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .team-member {
            background: rgba(0, 191, 255, 0.05);
            border: 1px solid rgba(0, 191, 255, 0.2);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .team-member:hover {
            transform: translateY(-5px);
            border-color: var(--primary-blue);
            box-shadow: var(--shadow-blue);
        }
        
        .team-avatar {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            color: var(--text-light);
        }
        
        .team-member h4 {
            color: var(--primary-blue);
            margin-bottom: 5px;
            font-family: 'Orbitron', monospace;
        }
        
        .team-role {
            color: var(--text-gray);
            font-size: 0.9rem;
            margin-bottom: 15px;
        }
        
        .team-description {
            color: var(--text-gray);
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .server-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .stat-card {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 191, 255, 0.2);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            border-color: var(--primary-blue);
            box-shadow: var(--shadow-blue);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.5rem;
            color: var(--text-light);
        }
        
        .stat-number {
            font-family: 'Orbitron', monospace;
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: var(--text-gray);
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .contact-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .contact-item {
                flex-direction: column;
                text-align: center;
            }
            
            .contact-item:hover {
                transform: translateY(-5px);
            }
        }
    </style>
</head>
<body>
    <div class="background-animation">
        <div class="floating-particles"></div>
    </div>

    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <i class="fas fa-gamepad"></i>
                    <span>ETERNAL</span>
                </div>
                
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="fas fa-home"></i>
                            <span>Ana Sayfa</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="rules.html" class="nav-link">
                            <i class="fas fa-scroll"></i>
                            <span>Kurallar</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="market.html" class="nav-link">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Market</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="contact.html" class="nav-link active">
                            <i class="fas fa-envelope"></i>
                            <span>İletişim</span>
                        </a>
                    </li>
                </ul>

                <div class="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <main class="main-content">
        <section class="contact-hero">
            <div class="contact-container">
                <h1 class="section-title">
                    <i class="fas fa-envelope"></i>
                    İletişim & Hakkımızda
                </h1>
                <p style="font-size: 1.2rem; color: var(--text-gray); margin-bottom: 0;">
                    Bizimle iletişime geçin ve Eternal Gaming ailesini tanıyın
                </p>
            </div>
        </section>

        <section style="padding: 50px 0;">
            <div class="contact-container">
                <div class="server-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="stat-number">2</div>
                        <div class="stat-label">Yıl Deneyim</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number">5000+</div>
                        <div class="stat-label">Kayıtlı Oyuncu</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number">99.9%</div>
                        <div class="stat-label">Uptime</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="stat-number">50+</div>
                        <div class="stat-label">Etkinlik</div>
                    </div>
                </div>

                <div class="contact-grid">
                    <div class="contact-info">
                        <h2 style="color: var(--primary-blue); margin-bottom: 30px; font-family: 'Orbitron', monospace;">
                            İletişim Bilgileri
                        </h2>
                        
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-server"></i>
                            </div>
                            <div class="contact-details">
                                <h4>Sunucu IP</h4>
                                <p>play.eternal.com:25565</p>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fab fa-discord"></i>
                            </div>
                            <div class="contact-details">
                                <h4>Discord Sunucusu</h4>
                                <p>discord.gg/eternal</p>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-details">
                                <h4>E-posta</h4>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-headset"></i>
                            </div>
                            <div class="contact-details">
                                <h4>Destek Saatleri</h4>
                                <p>7/24 Aktif Destek</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-form">
                        <h2 style="color: var(--primary-blue); margin-bottom: 30px; font-family: 'Orbitron', monospace;">
                            Bize Yazın
                        </h2>
                        
                        <form>
                            <div class="form-group">
                                <label for="name">Adınız</label>
                                <input type="text" id="name" name="name" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">E-posta</label>
                                <input type="email" id="email" name="email" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="subject">Konu</label>
                                <select id="subject" name="subject" required>
                                    <option value="">Konu Seçin</option>
                                    <option value="support">Teknik Destek</option>
                                    <option value="ban">Ban Kaldırma</option>
                                    <option value="suggestion">Öneri</option>
                                    <option value="partnership">Ortaklık</option>
                                    <option value="other">Diğer</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="message">Mesajınız</label>
                                <textarea id="message" name="message" placeholder="Mesajınızı buraya yazın..." required></textarea>
                            </div>
                            
                            <button type="submit" class="submit-btn">
                                <i class="fas fa-paper-plane"></i>
                                Mesaj Gönder
                            </button>
                        </form>
                    </div>
                </div>

                <div class="social-section">
                    <h2 style="color: var(--primary-blue); margin-bottom: 20px; font-family: 'Orbitron', monospace;">
                        Sosyal Medya
                    </h2>
                    <p style="color: var(--text-gray); margin-bottom: 0;">
                        Bizi sosyal medyada takip edin ve güncel haberlerden haberdar olun
                    </p>
                    
                    <div class="social-grid">
                        <a href="#" class="social-card">
                            <i class="fab fa-discord"></i>
                            <h4>Discord</h4>
                            <p>Toplulukla sohbet edin</p>
                        </a>
                        
                        <a href="#" class="social-card">
                            <i class="fab fa-youtube"></i>
                            <h4>YouTube</h4>
                            <p>Videolarımızı izleyin</p>
                        </a>
                        
                        <a href="#" class="social-card">
                            <i class="fab fa-instagram"></i>
                            <h4>Instagram</h4>
                            <p>Fotoğraflarımızı görün</p>
                        </a>
                        
                        <a href="#" class="social-card">
                            <i class="fab fa-twitter"></i>
                            <h4>Twitter</h4>
                            <p>Güncel duyurular</p>
                        </a>
                    </div>
                </div>

                <div class="team-section">
                    <h2 style="color: var(--primary-blue); margin-bottom: 20px; font-family: 'Orbitron', monospace; text-align: center;">
                        Yönetim Ekibi
                    </h2>
                    <p style="color: var(--text-gray); text-align: center; margin-bottom: 0;">
                        Eternal Gaming'i yöneten deneyimli ekibimizle tanışın
                    </p>
                    
                    <div class="team-grid">
                        <div class="team-member">
                            <div class="team-avatar">
                                <i class="fas fa-crown"></i>
                            </div>
                            <h4>EternalAdmin</h4>
                            <div class="team-role">Kurucu & Genel Müdür</div>
                            <p class="team-description">Sunucunun kurucusu ve genel koordinatörü. 5+ yıl Minecraft deneyimi.</p>
                        </div>
                        
                        <div class="team-member">
                            <div class="team-avatar">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h4>ModeratorX</h4>
                            <div class="team-role">Baş Moderatör</div>
                            <p class="team-description">Sunucu güvenliği ve oyuncu deneyiminden sorumlu baş moderatör.</p>
                        </div>
                        
                        <div class="team-member">
                            <div class="team-avatar">
                                <i class="fas fa-code"></i>
                            </div>
                            <h4>DevMaster</h4>
                            <div class="team-role">Geliştirici</div>
                            <p class="team-description">Plugin geliştirme ve teknik altyapıdan sorumlu geliştirici.</p>
                        </div>
                        
                        <div class="team-member">
                            <div class="team-avatar">
                                <i class="fas fa-paint-brush"></i>
                            </div>
                            <h4>BuilderPro</h4>
                            <div class="team-role">Baş Builder</div>
                            <p class="team-description">Sunucu haritaları ve yapılarının tasarımından sorumlu builder.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Eternal Gaming</h3>
                    <p>En iyi oyun deneyimi için buradayız!</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-discord"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h3>Hızlı Linkler</h3>
                    <ul>
                        <li><a href="index.html">Ana Sayfa</a></li>
                        <li><a href="rules.html">Kurallar</a></li>
                        <li><a href="market.html">Market</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Sunucu Bilgileri</h3>
                    <p>IP: play.eternal.com</p>
                    <p>Port: 25565</p>
                    <p>Versiyon: 1.20.1</p>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Eternal Gaming Server. Tüm hakları saklıdır.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
